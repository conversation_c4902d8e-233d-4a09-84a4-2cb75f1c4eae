# SipTracker

A location-based Flutter application with Material 3 design.

## Project Structure

```
lib/
├── main.dart                 # App entry point with Material 3 theme
├── screens/                  # Main screen widgets
│   ├── home_screen.dart      # Home/Discover screen
│   ├── map_screen.dart       # Map screen for displaying locations
│   ├── favorites_screen.dart # Favorites screen
│   ├── profile_screen.dart   # User profile screen
│   └── auth_screen.dart      # User authentication (login/registration)
├── widgets/                  # Reusable UI components
│   └── main_navigation.dart  # Bottom navigation implementation
├── models/                   # Data models (to be added)
├── services/                 # External service integrations
│   ├── supabase_service.dart # Supabase client service layer
│   └── location_service.dart # Location-specific database operations
└── utils/                    # Helper functions and constants
```

## Features

- ✅ Material 3 design system
- ✅ Bottom navigation with 4 tabs (Home, Map, Favorites, Profile)
- ✅ Proper state management for navigation
- ✅ Clean project structure following Flutter best practices
- ✅ Null safety enabled
- ✅ Basic error handling
- ✅ Supabase integration with service layer architecture
- ✅ Connection status indicator
- ✅ Ready for authentication and database operations
- ✅ Complete authentication UI with login/registration forms
- ✅ Form validation and user experience optimizations

## Navigation

The app uses a bottom navigation bar with the following screens:
- **Home** (🏠): Discover screen for finding locations
- **Map** (🗺️): Interactive map view
- **Favorites** (❤️): Saved favorite locations
- **Profile** (👤): User account and preferences

## Getting Started

1. Ensure you have Flutter installed
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the application
4. Run `flutter test` to execute tests

## Supabase Integration

The app includes a complete Supabase integration with:

- **Service Layer Architecture**: Clean separation of concerns with dedicated service classes
- **Error Handling**: Comprehensive error handling for network and database operations
- **Connection Status**: Visual indicator showing Supabase connection status
- **Ready for Production**: Placeholder credentials with TODO comments for secure configuration

### Setting up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Get your project URL and anon key from the project settings
3. Replace the placeholder values in `lib/main.dart`:
   ```dart
   await Supabase.initialize(
     url: 'YOUR_ACTUAL_SUPABASE_URL',
     anonKey: 'YOUR_ACTUAL_SUPABASE_ANON_KEY',
   );
   ```

### Using Supabase Services

```dart
// Get Supabase service instance
final supabaseService = SupabaseService();

// Check connection status
if (supabaseService.isInitialized) {
  // Perform database operations
  final locations = await LocationService().getAllLocations();
}

// Authentication
final user = supabaseService.currentUser;
final isLoggedIn = supabaseService.isAuthenticated;
```

### Authentication Screen

The app includes a complete authentication UI (`AuthScreen`) with:

- **Toggle Interface**: Switch between login and registration modes
- **Form Validation**: Email format, password strength, and confirmation matching
- **Material 3 Design**: Consistent with app theme and responsive layout
- **User Experience**: Loading states, error handling, and visual feedback
- **Ready for Integration**: Designed to easily connect with Supabase authentication

```dart
// Navigate to authentication screen
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const AuthScreen()),
);
```

## Development

- The app uses Material 3 design system (`useMaterial3: true`)
- Navigation is handled by `MainNavigation` widget using `IndexedStack`
- Each screen is a separate widget in the `screens/` directory
- Supabase integration follows service layer pattern for clean architecture
- Follow Flutter naming conventions (snake_case for files)
