# SipTracker

A location-based Flutter application with Material 3 design.

## Project Structure

```
lib/
├── main.dart                 # App entry point with Material 3 theme
├── screens/                  # Main screen widgets
│   ├── home_screen.dart      # Home/Discover screen
│   ├── map_screen.dart       # Map screen for displaying locations
│   ├── favorites_screen.dart # Favorites screen
│   └── profile_screen.dart   # User profile screen
├── widgets/                  # Reusable UI components
│   └── main_navigation.dart  # Bottom navigation implementation
├── models/                   # Data models (to be added)
├── services/                 # External service integrations (like Supabase)
└── utils/                    # Helper functions and constants
```

## Features

- ✅ Material 3 design system
- ✅ Bottom navigation with 4 tabs (Home, Map, Favorites, Profile)
- ✅ Proper state management for navigation
- ✅ Clean project structure following Flutter best practices
- ✅ Null safety enabled
- ✅ Basic error handling

## Navigation

The app uses a bottom navigation bar with the following screens:
- **Home** (🏠): Discover screen for finding locations
- **Map** (🗺️): Interactive map view
- **Favorites** (❤️): Saved favorite locations
- **Profile** (👤): User account and preferences

## Getting Started

1. Ensure you have Flutter installed
2. Run `flutter pub get` to install dependencies
3. Run `flutter run` to start the application
4. Run `flutter test` to execute tests

## Development

- The app uses Material 3 design system (`useMaterial3: true`)
- Navigation is handled by `MainNavigation` widget using `IndexedStack`
- Each screen is a separate widget in the `screens/` directory
- Follow Flutter naming conventions (snake_case for files)
