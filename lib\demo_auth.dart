import 'package:flutter/material.dart';
import 'screens/auth_screen.dart';

/// Demo app to showcase the AuthScreen independently
/// 
/// This file can be used to test the AuthScreen in isolation
/// by running: flutter run lib/demo_auth.dart
/// 
/// This is useful for development and testing purposes.
void main() {
  runApp(const AuthScreenDemo());
}

class AuthScreenDemo extends StatelessWidget {
  const AuthScreenDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SipTracker Auth Demo',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const AuthScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
