import 'package:flutter/material.dart';
import 'widgets/main_navigation.dart';

void main() {
  runApp(const SipTrackerApp());
}

class SipTrackerApp extends StatelessWidget {
  const SipTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SipTracker',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MainNavigation(),
      debugShowCheckedModeBanner: false,
    );
  }
}
