import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'widgets/main_navigation.dart';

/// Main entry point of the SipTracker application
///
/// Initializes Supabase before running the app and handles any initialization errors.
void main() async {
  // Ensure Flutter binding is initialized before calling async operations
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Supabase with configuration
    // TODO: Move these credentials to environment variables or secure configuration
    // For production, use a secure method to store and retrieve these values
    await Supabase.initialize(
      url: 'YOUR_SUPABASE_URL', // TODO: Replace with actual Supabase project URL
      anonKey: 'YOUR_SUPABASE_ANON_KEY', // TODO: Replace with actual Supabase anon key
    );

    // Run the app after successful initialization
    runApp(const SipTrackerApp());
  } catch (e) {
    // Handle Supabase initialization errors
    debugPrint('Failed to initialize Supabase: $e');

    // Run the app even if Supabase initialization fails
    // This allows the app to function in offline mode or with placeholder data
    runApp(const SipTrackerApp());
  }
}

class SipTrackerApp extends StatelessWidget {
  const SipTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SipTracker',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
      ),
      home: const MainNavigation(),
      debugShowCheckedModeBanner: false,
    );
  }
}
