// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:siptracker/main.dart';

void main() {
  testWidgets('SipTracker app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const SipTrackerApp());

    // Verify that the app loads with the bottom navigation
    expect(find.byType(BottomNavigationBar), findsOneWidget);

    // Verify that the home screen is displayed by default
    expect(find.text('Home Screen'), findsOneWidget);

    // Test navigation to map screen
    await tester.tap(find.byIcon(Icons.map));
    await tester.pump();

    // Verify that map screen is displayed
    expect(find.text('Map Screen'), findsOneWidget);
  });
}
